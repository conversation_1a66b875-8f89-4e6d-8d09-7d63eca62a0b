import { AbstractControl, ValidatorFn } from '@angular/forms';

export enum DigitsValidatorErrors {
    INVALID_LENGTH = 'INVALID_LENGTH',
    INVALID_DIGITS = 'INVALID_DIGITS',
}

export function DigitsValidator(minLength: number = 0, maxLength: number = 100): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
        if (control.pristine) {
            return null;
        }
        const value = control.value;
        if (!value) {
            return null;
        }
        const isValidLength = value.length >= minLength && value.length <= maxLength;
        if (!isValidLength) {
            return { error: DigitsValidatorErrors.INVALID_LENGTH };
        }
        const isOnlyDigits = /^\d+$/.test(value);
        if (!isOnlyDigits) {
            return { error: DigitsValidatorErrors.INVALID_DIGITS };
        }
        return null;
    };
}
