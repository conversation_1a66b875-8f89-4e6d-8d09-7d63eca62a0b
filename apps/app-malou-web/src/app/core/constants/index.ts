import { Day, MalouPeriod, PostPublicationStatus } from '@malou-io/package-utils';

import { TimePeriod } from ':shared/models/periods';
import { PhoneCode } from ':shared/models/phone-code';

export { countries } from './countries';

export const periodOptions = [
    { label: 'date_overview.last_seven_days', key: MalouPeriod.LAST_SEVEN_DAYS },
    { label: 'date_overview.last_thirty_days', key: MalouPeriod.LAST_THIRTY_DAYS },
    { label: 'date_overview.last_six_months', key: MalouPeriod.LAST_SIX_MONTHS },
    { label: 'date_overview.last_twelve_months', key: MalouPeriod.LAST_TWELVE_MONTHS },
];

export const notionLinks = Object.freeze({
    GENERAL: 'https://welcomehomemalou.notion.site/Foire-aux-questions-Malou-257b1ca546b747efaca1b63345da9603',
    RECONNECT_SOCIAL_NETWORKS:
        'https://welcomehomemalou.notion.site/Pourquoi-dois-je-reconnecter-mes-r-seaux-sociaux-784583aa4f1242dba71ab1ed852ce5bc',
    IMPORT_CLIENTS: 'https://welcomehomemalou.notion.site/Comment-t-l-charger-mes-donn-es-clients-9aaa6102625647e0a86f8564fb7f6496',
    BANNED_HASHTAGS:
        'https://welcomehomemalou.notion.site/Quels-sont-les-hashtags-bannis-par-Instagram-et-Facebook-83121e78b6aa4c5da32f2805ba2097fc',
    FB_PAGE:
        'https://welcomehomemalou.notion.site/Comment-changer-le-type-de-page-d-tablissement-Facebook-97c561ddabfb4a83b2eafcc727df2f44',
    AUTHORIZE_MESSAGES: 'https://welcomehomemalou.notion.site/Autoriser-l-acc-s-aux-messages-409ed4e746ee4641a4cdfa707357d04e',
    INFORMATIONS: 'https://welcomehomemalou.notion.site/Comment-mettre-jour-ses-informations-0c35c6217e764d769b1c8a6671dc8a35',
});

export const thirdPartyLinks = Object.freeze({
    REFERRAL_LINK: 'https://pages.viral-loops.com/share-fr-092024-referral-r3n94eqa',
});

export const commentStatus = Object.freeze({
    POSTED: {
        key: 'posted',
        fr: 'Posté',
        en: 'Posted',
    },
    PENDING: {
        key: 'pending',
        fr: 'En attente',
        en: 'Pending',
    },
    MANUALLY_POSTED: {
        key: 'manually_posted',
        fr: 'Réponse postée manuellement',
        en: 'Manually posted',
    },
});

export const DescriptionsSizes = Object.freeze({
    LONG: {
        key: 'long',
        maxLength: 600,
    },
    SHORT: {
        key: 'short',
        maxLength: 100,
    },
});

export const days = Object.freeze({
    [Day.MONDAY]: {
        digit: 1,
        fr: 'lundi',
        en: 'Monday',
        es: 'Lunes',
        it: 'Lunedì',
    },
    [Day.TUESDAY]: {
        digit: 2,
        fr: 'mardi',
        en: 'Tuesday',
        es: 'Martes',
        it: 'Martedì',
    },
    [Day.WEDNESDAY]: {
        digit: 3,
        fr: 'mercredi',
        en: 'Wednesday',
        es: 'Miércoles',
        it: 'Mercoledì',
    },
    [Day.THURSDAY]: {
        digit: 4,
        fr: 'jeudi',
        en: 'Thursday',
        es: 'Jueves',
        it: 'Giovedì',
    },
    [Day.FRIDAY]: {
        digit: 5,
        fr: 'vendredi',
        en: 'Friday',
        es: 'Viernes',
        it: 'Venerdì',
    },
    [Day.SATURDAY]: {
        digit: 6,
        fr: 'samedi',
        en: 'Saturday',
        es: 'Sábado',
        it: 'Sabato',
    },
    [Day.SUNDAY]: {
        digit: 7,
        fr: 'dimanche',
        en: 'Sunday',
        es: 'Domingo',
        it: 'Domenica',
    },
});

export enum ImgFormat {
    PNG = 'png',
    JPEG = 'jpeg',
    JPG = 'jpg',
}

export const downloadMedias = Object.freeze({
    LIMIT: 3,
});

export const dateFormat = Object.freeze({
    fr: 'DD-MM-yyyy',
});

export const PHONE_CODES: readonly PhoneCode[] = Object.freeze([
    { countryCode: 'FR', code: 33, text: '(+33) France', flagEmoji: '🇫🇷' },
    { countryCode: 'ES', code: 34, text: '(+34) Spain', flagEmoji: '🇪🇸' },
    { countryCode: 'GB', code: 44, text: '(+44) UK', flagEmoji: '🇬🇧' },
    { countryCode: 'US', code: 1, text: '(+1) USA', flagEmoji: '🇺🇸' },
    { countryCode: 'DZ', code: 213, text: '(+213) Algeria', flagEmoji: '🇩🇿' },
    { countryCode: 'AD', code: 376, text: '(+376) Andorra', flagEmoji: '🇦🇩' },
    { countryCode: 'AO', code: 244, text: '(+244) Angola', flagEmoji: '🇦🇴' },
    { countryCode: 'AI', code: 1264, text: '(+1264) Anguilla', flagEmoji: '🇦🇮' },
    { countryCode: 'AG', code: 1268, text: '(+1268) Antigua & Barbuda', flagEmoji: '🇦🇬' },
    { countryCode: 'AR', code: 54, text: '(+54) Argentina', flagEmoji: '🇦🇷' },
    { countryCode: 'AM', code: 374, text: '(+374) Armenia', flagEmoji: '🇦🇲' },
    { countryCode: 'AW', code: 297, text: '(+297) Aruba', flagEmoji: '🇦🇼' },
    { countryCode: 'AU', code: 61, text: '(+61) Australia', flagEmoji: '🇦🇺' },
    { countryCode: 'AT', code: 43, text: '(+43) Austria', flagEmoji: '🇦🇹' },
    { countryCode: 'AZ', code: 994, text: '(+994) Azerbaijan', flagEmoji: '🇦🇿' },
    { countryCode: 'BS', code: 1242, text: '(+1242) Bahamas', flagEmoji: '🇧🇸' },
    { countryCode: 'BH', code: 973, text: '(+973) Bahrain', flagEmoji: '🇧🇭' },
    { countryCode: 'BD', code: 880, text: '(+880) Bangladesh', flagEmoji: '🇧🇩' },
    { countryCode: 'BB', code: 1246, text: '(+1246) Barbados', flagEmoji: '🇧🇧' },
    { countryCode: 'BY', code: 375, text: '(+375) Belarus', flagEmoji: '🇧🇾' },
    { countryCode: 'BE', code: 32, text: '(+32) Belgium', flagEmoji: '🇧🇪' },
    { countryCode: 'BZ', code: 501, text: '(+501) Belize', flagEmoji: '🇧🇿' },
    { countryCode: 'BJ', code: 229, text: '(+229) Benin', flagEmoji: '🇧🇯' },
    { countryCode: 'BM', code: 1441, text: '(+1441) Bermuda', flagEmoji: '🇧🇲' },
    { countryCode: 'BT', code: 975, text: '(+975) Bhutan', flagEmoji: '🇧🇹' },
    { countryCode: 'BO', code: 591, text: '(+591) Bolivia', flagEmoji: '🇧🇴' },
    { countryCode: 'BA', code: 387, text: '(+387) Bosnia Herzegovina', flagEmoji: '🇧🇦' },
    { countryCode: 'BW', code: 267, text: '(+267) Botswana', flagEmoji: '🇧🇼' },
    { countryCode: 'BR', code: 55, text: '(+55) Brazil', flagEmoji: '🇧🇷' },
    { countryCode: 'BN', code: 673, text: '(+673) Brunei', flagEmoji: '🇧🇳' },
    { countryCode: 'BG', code: 359, text: '(+359) Bulgaria', flagEmoji: '🇧🇬' },
    { countryCode: 'BF', code: 226, text: '(+226) Burkina Faso', flagEmoji: '🇧🇫' },
    { countryCode: 'BI', code: 257, text: '(+257) Burundi', flagEmoji: '🇧🇮' },
    { countryCode: 'KH', code: 855, text: '(+855) Cambodia', flagEmoji: '🇰🇭' },
    { countryCode: 'CM', code: 237, text: '(+237) Cameroon', flagEmoji: '🇨🇲' },
    { countryCode: 'CA', code: 1, text: '(+1) Canada', flagEmoji: '🇨🇦' },
    { countryCode: 'CV', code: 238, text: '(+238) Cape Verde Islands', flagEmoji: '🇨🇻' },
    { countryCode: 'KY', code: 1345, text: '(+1345) Cayman Islands', flagEmoji: '🇰🇾' },
    { countryCode: 'CF', code: 236, text: '(+236) Central African Republic', flagEmoji: '🇨🇫' },
    { countryCode: 'CL', code: 56, text: '(+56) Chile', flagEmoji: '🇨🇱' },
    { countryCode: 'CN', code: 86, text: '(+86) China', flagEmoji: '🇨🇳' },
    { countryCode: 'CO', code: 57, text: '(+57) Colombia', flagEmoji: '🇨🇴' },
    { countryCode: 'KM', code: 269, text: '(+269) Comoros', flagEmoji: '🇰🇲' },
    { countryCode: 'CG', code: 242, text: '(+242) Congo', flagEmoji: '🇨🇬' },
    { countryCode: 'CK', code: 682, text: '(+682) Cook Islands', flagEmoji: '🇨🇰' },
    { countryCode: 'CR', code: 506, text: '(+506) Costa Rica', flagEmoji: '🇨🇷' },
    { countryCode: 'HR', code: 385, text: '(+385) Croatia', flagEmoji: '🇭🇷' },
    { countryCode: 'CU', code: 53, text: '(+53) Cuba', flagEmoji: '🇨🇺' },
    { countryCode: 'CY', code: 90392, text: '(+90392) Cyprus North', flagEmoji: '🇨🇾' },
    { countryCode: 'CY', code: 357, text: '(+357) Cyprus South', flagEmoji: '🇨🇾' },
    { countryCode: 'CZ', code: 42, text: '(+42) Czech Republic', flagEmoji: '🇨🇿' },
    { countryCode: 'DK', code: 45, text: '(+45) Denmark', flagEmoji: '🇩🇰' },
    { countryCode: 'DJ', code: 253, text: '(+253) Djibouti', flagEmoji: '🇩🇯' },
    { countryCode: 'DM', code: 1809, text: '(+1809) Dominica', flagEmoji: '🇩🇲' },
    { countryCode: 'DO', code: 1809, text: '(+1809) Dominican Republic', flagEmoji: '🇩🇴' },
    { countryCode: 'EC', code: 593, text: '(+593) Ecuador', flagEmoji: '🇪🇨' },
    { countryCode: 'EG', code: 20, text: '(+20) Egypt', flagEmoji: '🇪🇬' },
    { countryCode: 'SV', code: 503, text: '(+503) El Salvador', flagEmoji: '🇸🇻' },
    { countryCode: 'GQ', code: 240, text: '(+240) Equatorial Guinea', flagEmoji: '🇬🇶' },
    { countryCode: 'ER', code: 291, text: '(+291) Eritrea', flagEmoji: '🇪🇷' },
    { countryCode: 'EE', code: 372, text: '(+372) Estonia', flagEmoji: '🇪🇪' },
    { countryCode: 'ET', code: 251, text: '(+251) Ethiopia', flagEmoji: '🇪🇹' },
    { countryCode: 'FK', code: 500, text: '(+500) Falkland Islands', flagEmoji: '🇫🇰' },
    { countryCode: 'FO', code: 298, text: '(+298) Faroe Islands', flagEmoji: '🇫🇴' },
    { countryCode: 'FJ', code: 679, text: '(+679) Fiji', flagEmoji: '🇫🇯' },
    { countryCode: 'FI', code: 358, text: '(+358) Finland', flagEmoji: '🇫🇮' },
    { countryCode: 'GF', code: 594, text: '(+594) French Guiana', flagEmoji: '🇬🇫' },
    { countryCode: 'PF', code: 689, text: '(+689) French Polynesia', flagEmoji: '🇵🇫' },
    { countryCode: 'GA', code: 241, text: '(+241) Gabon', flagEmoji: '🇬🇦' },
    { countryCode: 'GM', code: 220, text: '(+220) Gambia', flagEmoji: '🇬🇲' },
    { countryCode: 'GE', code: 7880, text: '(+7880) Georgia', flagEmoji: '🇬🇪' },
    { countryCode: 'DE', code: 49, text: '(+49) Germany', flagEmoji: '🇩🇪' },
    { countryCode: 'GH', code: 233, text: '(+233) Ghana', flagEmoji: '🇬🇭' },
    { countryCode: 'GI', code: 350, text: '(+350) Gibraltar', flagEmoji: '🇬🇮' },
    { countryCode: 'GR', code: 30, text: '(+30) Greece', flagEmoji: '🇬🇷' },
    { countryCode: 'GL', code: 299, text: '(+299) Greenland', flagEmoji: '🇬🇱' },
    { countryCode: 'GD', code: 1473, text: '(+1473) Grenada', flagEmoji: '🇬🇩' },
    { countryCode: 'GP', code: 590, text: '(+590) Guadeloupe', flagEmoji: '🇬🇵' },
    { countryCode: 'GU', code: 671, text: '(+671) Guam', flagEmoji: '🇬🇺' },
    { countryCode: 'GT', code: 502, text: '(+502) Guatemala', flagEmoji: '🇬🇹' },
    { countryCode: 'GN', code: 224, text: '(+224) Guinea', flagEmoji: '🇬🇳' },
    { countryCode: 'GW', code: 245, text: '(+245) Guinea - Bissau', flagEmoji: '🇬🇼' },
    { countryCode: 'GY', code: 592, text: '(+592) Guyana', flagEmoji: '🇬🇾' },
    { countryCode: 'HT', code: 509, text: '(+509) Haiti', flagEmoji: '🇭🇹' },
    { countryCode: 'HN', code: 504, text: '(+504) Honduras', flagEmoji: '🇭🇳' },
    { countryCode: 'HK', code: 852, text: '(+852) Hong Kong', flagEmoji: '🇭🇰' },
    { countryCode: 'HU', code: 36, text: '(+36) Hungary', flagEmoji: '🇭🇺' },
    { countryCode: 'IS', code: 354, text: '(+354) Iceland', flagEmoji: '🇮🇸' },
    { countryCode: 'IN', code: 91, text: '(+91) India', flagEmoji: '🇮🇳' },
    { countryCode: 'ID', code: 62, text: '(+62) Indonesia', flagEmoji: '🇮🇩' },
    { countryCode: 'IR', code: 98, text: '(+98) Iran', flagEmoji: '🇮🇷' },
    { countryCode: 'IQ', code: 964, text: '(+964) Iraq', flagEmoji: '🇮🇶' },
    { countryCode: 'IE', code: 353, text: '(+353) Ireland', flagEmoji: '🇮🇪' },
    { countryCode: 'IL', code: 972, text: '(+972) Israel', flagEmoji: '🇮🇱' },
    { countryCode: 'IT', code: 39, text: '(+39) Italy', flagEmoji: '🇮🇹' },
    { countryCode: 'JM', code: 1876, text: '(+1876) Jamaica', flagEmoji: '🇯🇲' },
    { countryCode: 'JP', code: 81, text: '(+81) Japan', flagEmoji: '🇯🇵' },
    { countryCode: 'JO', code: 962, text: '(+962) Jordan', flagEmoji: '🇯🇴' },
    { countryCode: 'KZ', code: 7, text: '(+7) Kazakhstan', flagEmoji: '🇰🇿' },
    { countryCode: 'KE', code: 254, text: '(+254) Kenya', flagEmoji: '🇰🇪' },
    { countryCode: 'KI', code: 686, text: '(+686) Kiribati', flagEmoji: '🇰🇮' },
    { countryCode: 'KP', code: 850, text: '(+850) Korea North', flagEmoji: '🇰🇵' },
    { countryCode: 'KR', code: 82, text: '(+82) Korea South', flagEmoji: '🇰🇷' },
    { countryCode: 'KW', code: 965, text: '(+965) Kuwait', flagEmoji: '🇰🇼' },
    { countryCode: 'KG', code: 996, text: '(+996) Kyrgyzstan', flagEmoji: '🇰🇬' },
    { countryCode: 'LA', code: 856, text: '(+856) Laos', flagEmoji: '🇱🇦' },
    { countryCode: 'LV', code: 371, text: '(+371) Latvia', flagEmoji: '🇱🇻' },
    { countryCode: 'LB', code: 961, text: '(+961) Lebanon', flagEmoji: '🇱🇧' },
    { countryCode: 'LS', code: 266, text: '(+266) Lesotho', flagEmoji: '🇱🇸' },
    { countryCode: 'LR', code: 231, text: '(+231) Liberia', flagEmoji: '🇱🇷' },
    { countryCode: 'LY', code: 218, text: '(+218) Libya', flagEmoji: '🇱🇾' },
    { countryCode: 'LI', code: 417, text: '(+417) Liechtenstein', flagEmoji: '🇱🇮' },
    { countryCode: 'LT', code: 370, text: '(+370) Lithuania', flagEmoji: '🇱🇹' },
    { countryCode: 'LU', code: 352, text: '(+352) Luxembourg', flagEmoji: '🇱🇺' },
    { countryCode: 'MO', code: 853, text: '(+853) Macao', flagEmoji: '🇲🇴' },
    { countryCode: 'MK', code: 389, text: '(+389) Macedonia', flagEmoji: '🇲🇰' },
    { countryCode: 'MG', code: 261, text: '(+261) Madagascar', flagEmoji: '🇲🇬' },
    { countryCode: 'MW', code: 265, text: '(+265) Malawi', flagEmoji: '🇲🇼' },
    { countryCode: 'MY', code: 60, text: '(+60) Malaysia', flagEmoji: '🇲🇾' },
    { countryCode: 'MV', code: 960, text: '(+960) Maldives', flagEmoji: '🇲🇻' },
    { countryCode: 'ML', code: 223, text: '(+223) Mali', flagEmoji: '🇲🇱' },
    { countryCode: 'MT', code: 356, text: '(+356) Malta', flagEmoji: '🇲🇹' },
    { countryCode: 'MH', code: 692, text: '(+692) Marshall Islands', flagEmoji: '🇲🇭' },
    { countryCode: 'MQ', code: 596, text: '(+596) Martinique', flagEmoji: '🇲🇶' },
    { countryCode: 'MR', code: 222, text: '(+222) Mauritania', flagEmoji: '🇲🇷' },
    { countryCode: 'YT', code: 269, text: '(+269) Mayotte', flagEmoji: '🇾🇹' },
    { countryCode: 'MX', code: 52, text: '(+52) Mexico', flagEmoji: '🇲🇽' },
    { countryCode: 'FM', code: 691, text: '(+691) Micronesia', flagEmoji: '🇫🇲' },
    { countryCode: 'MD', code: 373, text: '(+373) Moldova', flagEmoji: '🇲🇩' },
    { countryCode: 'MC', code: 377, text: '(+377) Monaco', flagEmoji: '🇲🇨' },
    { countryCode: 'MN', code: 976, text: '(+976) Mongolia', flagEmoji: '🇲🇳' },
    { countryCode: 'MS', code: 1664, text: '(+1664) Montserrat', flagEmoji: '🇲🇸' },
    { countryCode: 'MA', code: 212, text: '(+212) Morocco', flagEmoji: '🇲🇦' },
    { countryCode: 'MZ', code: 258, text: '(+258) Mozambique', flagEmoji: '🇲🇿' },
    { countryCode: 'MN', code: 95, text: '(+95) Myanmar', flagEmoji: '🇲🇲' },
    { countryCode: 'NA', code: 264, text: '(+264) Namibia', flagEmoji: '🇳🇦' },
    { countryCode: 'NR', code: 674, text: '(+674) Nauru', flagEmoji: '🇳🇷' },
    { countryCode: 'NP', code: 977, text: '(+977) Nepal', flagEmoji: '🇳🇵' },
    { countryCode: 'NL', code: 31, text: '(+31) Netherlands', flagEmoji: '🇳🇱' },
    { countryCode: 'NC', code: 687, text: '(+687) New Caledonia', flagEmoji: '🇳🇨' },
    { countryCode: 'NZ', code: 64, text: '(+64) New Zealand', flagEmoji: '🇳🇿' },
    { countryCode: 'NI', code: 505, text: '(+505) Nicaragua', flagEmoji: '🇳🇮' },
    { countryCode: 'NE', code: 227, text: '(+227) Niger', flagEmoji: '🇳🇪' },
    { countryCode: 'NG', code: 234, text: '(+234) Nigeria', flagEmoji: '🇳🇬' },
    { countryCode: 'NU', code: 683, text: '(+683) Niue', flagEmoji: '🇳🇺' },
    { countryCode: 'NF', code: 672, text: '(+672) Norfolk Islands', flagEmoji: '🇳🇫' },
    { countryCode: 'NP', code: 670, text: '(+670) Northern Marianas', flagEmoji: '🇲🇵' },
    { countryCode: 'NO', code: 47, text: '(+47) Norway', flagEmoji: '🇳🇴' },
    { countryCode: 'OM', code: 968, text: '(+968) Oman', flagEmoji: '🇴🇲' },
    { countryCode: 'PW', code: 680, text: '(+680) Palau', flagEmoji: '🇵🇼' },
    { countryCode: 'PA', code: 507, text: '(+507) Panama', flagEmoji: '🇵🇦' },
    { countryCode: 'PG', code: 675, text: '(+675) Papua New Guinea', flagEmoji: '🇵🇬' },
    { countryCode: 'PY', code: 595, text: '(+595) Paraguay', flagEmoji: '🇵🇾' },
    { countryCode: 'PE', code: 51, text: '(+51) Peru', flagEmoji: '🇵🇪' },
    { countryCode: 'PH', code: 63, text: '(+63) Philippines', flagEmoji: '🇵🇭' },
    { countryCode: 'PL', code: 48, text: '(+48) Poland', flagEmoji: '🇵🇱' },
    { countryCode: 'PT', code: 351, text: '(+351) Portugal', flagEmoji: '🇵🇹' },
    { countryCode: 'PR', code: 1787, text: '(+1787) Puerto Rico', flagEmoji: '🇵🇷' },
    { countryCode: 'QA', code: 974, text: '(+974) Qatar', flagEmoji: '🇶🇦' },
    { countryCode: 'RE', code: 262, text: '(+262) Reunion', flagEmoji: '🇷🇪' },
    { countryCode: 'RO', code: 40, text: '(+40) Romania', flagEmoji: '🇷🇴' },
    { countryCode: 'RU', code: 7, text: '(+7) Russia', flagEmoji: '🇷🇺' },
    { countryCode: 'RW', code: 250, text: '(+250) Rwanda', flagEmoji: '🇷🇼' },
    { countryCode: 'SM', code: 378, text: '(+378) San Marino', flagEmoji: '🇸🇲' },
    { countryCode: 'ST', code: 239, text: '(+239) Sao Tome &amp; Principe', flagEmoji: '🇸🇹' },
    { countryCode: 'SA', code: 966, text: '(+966) Saudi Arabia', flagEmoji: '🇸🇦' },
    { countryCode: 'SN', code: 221, text: '(+221) Senegal', flagEmoji: '🇸🇳' },
    { countryCode: 'CS', code: 381, text: '(+381) Serbia', flagEmoji: '🇷🇸' },
    { countryCode: 'SC', code: 248, text: '(+248) Seychelles', flagEmoji: '🇸🇨' },
    { countryCode: 'SL', code: 232, text: '(+232) Sierra Leone', flagEmoji: '🇸🇱' },
    { countryCode: 'SG', code: 65, text: '(+65) Singapore', flagEmoji: '🇸🇬' },
    { countryCode: 'SK', code: 421, text: '(+421) Slovak Republic', flagEmoji: '🇸🇰' },
    { countryCode: 'SI', code: 386, text: '(+386) Slovenia', flagEmoji: '🇸🇮' },
    { countryCode: 'SB', code: 677, text: '(+677) Solomon Islands', flagEmoji: '🇸🇧' },
    { countryCode: 'SO', code: 252, text: '(+252) Somalia', flagEmoji: '🇸🇴' },
    { countryCode: 'ZA', code: 27, text: '(+27) South Africa', flagEmoji: '🇿🇦' },
    { countryCode: 'LK', code: 94, text: '(+94) Sri Lanka', flagEmoji: '🇱🇰' },
    { countryCode: 'SH', code: 290, text: '(+290) St. Helena', flagEmoji: '🇸🇭' },
    { countryCode: 'KN', code: 1869, text: '(+1869) St. Kitts', flagEmoji: '🇰🇳' },
    { countryCode: 'SC', code: 1758, text: '(+1758) St. Lucia', flagEmoji: '🇱🇨' },
    { countryCode: 'SD', code: 249, text: '(+249) Sudan', flagEmoji: '🇸🇩' },
    { countryCode: 'SR', code: 597, text: '(+597) Suriname', flagEmoji: '🇸🇷' },
    { countryCode: 'SZ', code: 268, text: '(+268) Swaziland', flagEmoji: '🇸🇿' },
    { countryCode: 'SE', code: 46, text: '(+46) Sweden', flagEmoji: '🇸🇪' },
    { countryCode: 'CH', code: 41, text: '(+41) Switzerland', flagEmoji: '🇨🇭' },
    { countryCode: 'SI', code: 963, text: '(+963) Syria', flagEmoji: '🇸🇾' },
    { countryCode: 'TW', code: 886, text: '(+886) Taiwan', flagEmoji: '🇹🇼' },
    { countryCode: 'TJ', code: 7, text: '(+7) Tajikstan', flagEmoji: '🇹🇯' },
    { countryCode: 'TH', code: 66, text: '(+66) Thailand', flagEmoji: '🇹🇭' },
    { countryCode: 'TG', code: 228, text: '(+228) Togo', flagEmoji: '🇹🇬' },
    { countryCode: 'TO', code: 676, text: '(+676) Tonga', flagEmoji: '🇹🇴' },
    { countryCode: 'TT', code: 1868, text: '(+1868) Trinidad &amp; Tobago', flagEmoji: '🇹🇹' },
    { countryCode: 'TN', code: 216, text: '(+216) Tunisia', flagEmoji: '🇹🇳' },
    { countryCode: 'TR', code: 90, text: '(+90) Turkey', flagEmoji: '🇹🇷' },
    { countryCode: 'TM', code: 7, text: '(+7) Turkmenistan', flagEmoji: '🇹🇲' },
    { countryCode: 'TM', code: 993, text: '(+993) Turkmenistan', flagEmoji: '🇹🇲' },
    {
        countryCode: 'TC',
        code: 1649,
        text: '(+1649) Turks &amp; Caicos Islands',
        flagEmoji: '🇹🇨',
    },
    { countryCode: 'TV', code: 688, text: '(+688) Tuvalu', flagEmoji: '🇹🇻' },
    { countryCode: 'UG', code: 256, text: '(+256) Uganda', flagEmoji: '🇺🇬' },
    { countryCode: 'UA', code: 380, text: '(+380) Ukraine', flagEmoji: '🇺🇦' },
    { countryCode: 'AE', code: 971, text: '(+971) United Arab Emirates', flagEmoji: '🇦🇪' },
    { countryCode: 'UY', code: 598, text: '(+598) Uruguay', flagEmoji: '🇺🇾' },
    { countryCode: 'UZ', code: 7, text: '(+7) Uzbekistan', flagEmoji: '🇺🇿' },
    { countryCode: 'VU', code: 678, text: '(+678) Vanuatu', flagEmoji: '🇻🇺' },
    { countryCode: 'VA', code: 379, text: '(+379) Vatican City', flagEmoji: '🇻🇦' },
    { countryCode: 'VE', code: 58, text: '(+58) Venezuela', flagEmoji: '🇻🇪' },
    { countryCode: 'VN', code: 84, text: '(+84) Vietnam', flagEmoji: '🇻🇳' },
    { countryCode: 'VG', code: 84, text: '(+1284) Virgin Islands - British', flagEmoji: '🇻🇬' },
    { countryCode: 'VI', code: 84, text: '(+1340) Virgin Islands - US', flagEmoji: '🇻🇮' },
    { countryCode: 'WF', code: 681, text: '(+681) Wallis &amp; Futuna', flagEmoji: '🇼🇫' },
    { countryCode: 'YE', code: 969, text: ')(+969) Yemen (Nort', flagEmoji: '🇾🇪' },
    { countryCode: 'YE', code: 967, text: ')(+967) Yemen (Sout', flagEmoji: '🇾🇪' },
    { countryCode: 'ZM', code: 260, text: '(+260) Zambia', flagEmoji: '🇿🇲' },
    { countryCode: 'ZW', code: 263, text: '(+263) Zimbabwe', flagEmoji: '🇿🇼' },
    { countryCode: 'CI', code: 225, text: '(+225) Ivory Coast', flagEmoji: '🇨🇮' },
]);

export const ratingText = {
    RATING_GOOD: 'Avis de client satisfait (4-5 étoiles)',
    RATING_NEUTRAL: 'Avis de client neutre (3 étoiles)',
    RATING_BAD: 'Avis de client mécontent (1-2 étoiles)',
};

export const ratings = [ratingText.RATING_GOOD, ratingText.RATING_NEUTRAL, ratingText.RATING_BAD];

export const ratingEval = {
    GOOD_RATING: 'GOOD',
    NEUTRAL_RATING: 'NEUTRAL',
    BAD_RATING: 'BAD',
    NO_RATING: 'NO_RATING',
};

export const maxHashtagInIgPosts = 30;
export const eventTitleTextLimit = 58;
export const postCaptionTextLimit = 1500;
export const mapsterPostCaptionTextLimit = 300;

export const bannedHashtags = Object.freeze([
    'abdl',
    'addmysc',
    'adulting',
    'alone',
    'always',
    'armparty',
    'asiangirl',
    'ass',
    'assday',
    'attractive',
    'beautyblogger',
    'besties',
    'bikinibody',
    'boho',
    'brain',
    'babyrp',
    'bacak',
    'baddie',
    'balenciaga',
    'costumes',
    'curvygirls',
    'customers',
    'date',
    'dating',
    'desk',
    'direct',
    'dm',
    'edm',
    'eggplant',
    'elevator',
    'fitnessgirls',
    'fishnets',
    'girlsonly',
    'gloves',
    'hardworkpaysoff',
    'hawks',
    'hotweather',
    'humpday',
    'hustler',
    'ice',
    'instasport',
    'iphonegraphy',
    'italiano',
    'kansas',
    'kickoff',
    'killingit',
    'kissing',
    'loseweight',
    'lulu',
    'leaves',
    'like',
    'lean',
    'lingerie',
    'master',
    'mileycyrus',
    'milf',
    'mirrorphoto',
    'models',
    'mustfollow',
    'nasty',
    'newyearsday',
    'nudity',
    'overnight',
    'orderweedonline',
    'parties',
    'petite',
    'pornfood',
    'prettygirl',
    'pushups',
    'qatar',
    'rate',
    'ravens',
    'saltwater',
    'samelove',
    'selfharm',
    'single',
    'singlelife',
    'skateboarding',
    'skype',
    'snap',
    'snapchat',
    'tag4like',
    'tagsforlikes',
    'tanlines',
    'todayimwearing',
    'teens',
    'teen',
    'thought',
    'undies',
    'underage',
    'valentinesday',
    'workflow',
    'wtf',
    'xanax',
    'Youngmodel',
]);
export const refreshTime = Object.freeze({
    socialPosts: 60000 * 15,
});

// hours of the day with interval of 15min
export const times: string[] = [].concat
    .apply(
        ['24:00'],
        [
            '00',
            '01',
            '02',
            '03',
            '04',
            '05',
            '06',
            '07',
            '08',
            '09',
            '10',
            '11',
            '12',
            '13',
            '14',
            '15',
            '16',
            '17',
            '18',
            '19',
            '20',
            '21',
            '22',
            '23',
        ].map((hour) => ['00', '15', '30', '45'].map((minutes) => `${hour}:${minutes}`))
    )
    .sort();

export enum AutoSaveState {
    SAVED = 'SAVED',
    NOT_SAVING = 'NOT_SAVING',
    SAVING = 'SAVING',
}

export enum ExtendedPostPublicationStatus {
    PENDING = PostPublicationStatus.PENDING,
    PUBLISHED = PostPublicationStatus.PUBLISHED,
    REJECTED = PostPublicationStatus.REJECTED,
    DRAFT = PostPublicationStatus.DRAFT,
    ERROR = PostPublicationStatus.ERROR,
    PUBLISHING = 'publishing',
    ACTIVE = 'active',
}

export const postsUpdateTexts = Object.freeze({
    GMB: ['Les horaires ont été mises à jour.', 'Hours were updated.'],
    FACEBOOK: [/a changé sa photo de/, /a actualisé son statut/, /updated their profile picture/, /updated their cover photo/],
});

export const breakpoints = Object.freeze({
    small: 639,
    medium: 769,
    large: 1023,
    very_large: 1279,
});

export const MALOU_MANAGER_EMAIL_ADDRESS = '<EMAIL>';

export const adminAuthList = ['<EMAIL>', '232142364148787', '107323322263317'];

export const Mo = 1_000_000;

export const businessHours = Object.freeze({
    maxTimeSlotsPerDay: 3,
});

export const DEFAULT_CLOSED_TIME_PERIODS = Object.freeze(
    Object.keys(days)
        .map((day) => ({
            isClosed: true,
            openDay: day as Day,
            openTime: '10:00',
            closeDay: day as Day,
            closeTime: '22:30',
        }))
        .map((period) => new TimePeriod(period))
);

export enum MediaEditionState {
    EDITING,
    FINISHED_EDITING,
}

export enum BindingIdKey {
    BINDING_ID = 'bindingId',
    MALOU_STORY_ID = 'malouStoryId',
}

export interface IStoryVideoCheck {
    duration: number;
    maxDuration: number;
    minDuration: number;
    isDurationValid: boolean;
    width: number;
    maxWidth: number;
    isWidthValid: boolean;
    height: number;
    ratio: number;
    maxRatio: number;
    minRatio: number;
    isRatioValid: boolean;
    extension: string;
    isHeightValid: boolean;
    isExtensionValid: boolean;
    error: boolean;
}

export const STARS_RATING = [1, 2, 3, 4, 5];

export enum StarValue {
    UNKNOWN = -1,
    ONE = 1,
    TWO = 2,
    THREE = 3,
    FOUR = 4,
    FIVE = 5,
}
export const GIFT_PERCENTAGE_DECIMALS = 1;
// ################### JIMO ###################

export type JimoType = Window & typeof globalThis & { jimo: any[] } & { jimoInit: () => void };
export const JimoSupportedLanguages = ['en', 'fr', 'es', 'pt', 'it', 'de', 'jp', 'zh', 'sk'];
