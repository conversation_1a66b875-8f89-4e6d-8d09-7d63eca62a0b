import { Component, computed, OnInit, Signal, signal, WritableSignal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { PHONE_CODES } from ':core/constants';
import { ToastService } from ':core/services/toast.service';
import { UsersService } from ':modules/user/users.service';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { SelectComponent } from ':shared/components/select/select.component';
import { getFormControlRecordFromDefaultValue } from ':shared/helpers/form-control-from-default-value';
import { INullableFormGroup } from ':shared/interfaces/form-control-record.interface';
import { HttpErrorPipe } from ':shared/pipes/http-error.pipe';
import { IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';
import { DigitsValidator, DigitsValidatorErrors } from ':shared/validators/digits.validator';

import * as UserActions from '../store/user.actions';
import { UserState } from '../store/user.reducer';
import { User } from '../user';

type PhoneCode = (typeof PHONE_CODES)[number];
interface MiscForm {
    name: string;
    lastname: string;
    phone: {
        prefix?: PhoneCode | null;
        digits?: number | null;
    };
}

@Component({
    selector: 'app-edit-user-miscellaneous',
    templateUrl: './edit-user-miscellaneous.component.html',
    styleUrls: ['./edit-user-miscellaneous.component.scss'],
    imports: [
        FormsModule,
        ReactiveFormsModule,
        InputTextComponent,
        SelectComponent,
        MatButtonModule,
        IllustrationPathResolverPipe,
        ImagePathResolverPipe,
        TranslateModule,
    ],
})
export class EditUserMiscellaneousComponent implements OnInit {
    public miscForm: INullableFormGroup<MiscForm>;
    public user: User;
    public PHONE_CODES = [...PHONE_CODES];

    phonePrefixSignal: Signal<PhoneCode | null>;
    digitsErrorMessage: WritableSignal<string | undefined> = signal(undefined);
    formattedPrefix: Signal<string> = computed(() => {
        const prefix = this.phonePrefixSignal?.();
        if (!prefix) {
            return '';
        }
        return `+${prefix.code}`;
    });

    get phonePrefix(): FormControl<PhoneCode | null> {
        return this.miscForm.get(['phone', 'prefix']) as FormControl<PhoneCode | null>;
    }

    get phoneDigits(): FormControl<number | null> {
        return this.miscForm.get(['phone', 'digits']) as FormControl<number | null>;
    }

    constructor(
        private readonly _activatedRoute: ActivatedRoute,
        private readonly _usersService: UsersService,
        private readonly _router: Router,
        private readonly _fb: FormBuilder,
        private readonly _store: Store<{ user: UserState }>,
        private readonly _toastService: ToastService,
        private readonly _httpErrorPipe: HttpErrorPipe,
        private readonly _translateService: TranslateService
    ) {}

    ngOnInit(): void {
        this._activatedRoute.data.subscribe((val) => {
            this.user = val.user;
            const formRecord = getFormControlRecordFromDefaultValue({
                name: this.user.name || '',
                lastname: this.user.lastname || '',
                phone: new FormGroup({
                    prefix: new FormControl(
                        this.user.phone?.prefix ? this.PHONE_CODES.find((pc) => pc.code === this.user.phone!.prefix)! : this.PHONE_CODES[0]
                    ),
                    digits: new FormControl(this.user.phone?.digits ?? null, [DigitsValidator(0, 20)]),
                }),
            });
            this.miscForm = this._fb.group(formRecord);

            this.phonePrefixSignal = toSignal(this.phonePrefix.valueChanges, {
                initialValue: this.miscForm.get(['phone', 'prefix'])?.value ?? null,
            });

            this.phoneDigits.statusChanges.subscribe(() => {
                switch (this.phoneDigits.errors?.error) {
                    case DigitsValidatorErrors.INVALID_LENGTH:
                        this.digitsErrorMessage.set(this._translateService.instant('admin.users.phone_length_error'));
                        break;
                    case DigitsValidatorErrors.INVALID_DIGITS:
                        this.digitsErrorMessage.set(this._translateService.instant('admin.users.phone_format_error'));
                        break;
                    default:
                        this.digitsErrorMessage.set(undefined);
                        break;
                }
            });
        });
    }

    cancel(): void {
        this._router.navigate(['..'], { relativeTo: this._activatedRoute });
    }

    phoneCodesDisplayWith(phoneCode: any): string {
        return phoneCode?.text || '';
    }

    updateMisc(): void {
        const phoneValue = this.miscForm.value.phone;
        this._usersService
            .updateUser$(this.user._id, {
                name: this.miscForm.value.name ?? undefined,
                lastname: this.miscForm.value.lastname ?? undefined,
                phone:
                    phoneValue?.prefix && phoneValue?.digits
                        ? {
                              prefix: (phoneValue.prefix as PhoneCode).code,
                              digits: phoneValue.digits,
                          }
                        : undefined,
            })
            .subscribe({
                next: (res: any) => {
                    this._store.dispatch(UserActions.editUserInfos({ infos: new User(res.data) }));
                    this._router.navigate(['.']);
                },
                error: (err) => {
                    console.warn('err :>>', err);
                    this._toastService.openErrorToast(this._httpErrorPipe.transform(err?.error?.message || err?.message || err));
                },
            });
    }
}
