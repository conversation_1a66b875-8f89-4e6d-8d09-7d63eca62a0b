import { singleton } from 'tsyringe';

import { AdminSearchUsersQueryDto, AdminSearchUsersResponseDto, AdminSearchUsersUserDto } from '@malou-io/package-dto';
import { DbId, ReadPreferenceMode, toDbId } from '@malou-io/package-models';

import { toDiacriticInsensitiveRegexString } from ':helpers/utils';
import { UsersRepository } from ':modules/users/users.repository';

@singleton()
export class AdminSearchUsersUseCase {
    constructor(private readonly _usersRepository: UsersRepository) {}

    async execute(params: AdminSearchUsersQueryDto): Promise<AdminSearchUsersResponseDto> {
        const { text, limit = 20, offset = 0 } = params;

        const trimmedText = text?.trim();

        if (trimmedText) {
            return this._executeWithTextFilter(trimmedText, limit, offset);
        } else {
            return this._executeWithoutTextFilter(limit, offset); // faster
        }
    }

    private async _executeWithTextFilter(text: string, limit: number, offset: number): Promise<AdminSearchUsersResponseDto> {
        const pipeline: any[] = [];

        pipeline.push({
            $lookup: {
                from: 'organizations',
                localField: 'organizationIds',
                foreignField: '_id',
                as: 'organizations',
            },
        });

        pipeline.push({
            $lookup: {
                from: 'userrestaurants',
                localField: '_id',
                foreignField: 'userId',
                as: 'restaurants',
                pipeline: [
                    {
                        $lookup: {
                            from: 'restaurants',
                            localField: 'restaurantId',
                            foreignField: '_id',
                            as: 'restaurantDetails',
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                        active: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $match: { 'restaurantDetails.active': true },
                    },
                    {
                        $addFields: {
                            restaurantDetails: { $arrayElemAt: ['$restaurantDetails', 0] },
                        },
                    },
                ],
            },
        });

        const searchRegex = toDiacriticInsensitiveRegexString(text);

        let dbId: DbId | null = null;
        try {
            dbId = toDbId(text);
        } catch (err) {
            // ignore
        }

        pipeline.push({
            $match: {
                $or: [
                    { email: { $regex: searchRegex, $options: 'i' } },
                    { 'organizations.name': { $regex: searchRegex, $options: 'i' } },
                    { _id: dbId },
                ],
            },
        });

        pipeline.push({
            $sort: {
                createdAt: -1,
                email: 1,
            },
        });

        const countPipeline = [...pipeline];
        countPipeline.push({ $count: 'total' });

        pipeline.push({ $skip: offset });
        pipeline.push({ $limit: limit });

        const [users, countResult] = await Promise.all([
            this._usersRepository.aggregate(pipeline, {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'adminSearchUsersWithText',
            }),
            this._usersRepository.aggregate(countPipeline, {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'adminSearchUsersWithTextCount',
            }),
        ]);

        const total = countResult.length > 0 ? countResult[0].total : 0;

        return {
            data: users.map((user) => this._toDto(user)),
            total,
        };
    }

    private async _executeWithoutTextFilter(limit: number, offset: number): Promise<AdminSearchUsersResponseDto> {
        const totalCountResult = await this._usersRepository.aggregate([{ $count: 'total' }], {
            readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
            comment: 'adminSearchUsersCountOnly',
        });
        const total = totalCountResult.length > 0 ? totalCountResult[0].total : 0;

        const pipeline: any[] = [];

        pipeline.push({
            $sort: {
                createdAt: -1,
                email: 1,
            },
        });

        pipeline.push({ $skip: offset });
        pipeline.push({ $limit: limit });

        pipeline.push({
            $lookup: {
                from: 'organizations',
                localField: 'organizationIds',
                foreignField: '_id',
                as: 'organizations',
            },
        });

        pipeline.push({
            $lookup: {
                from: 'userrestaurants',
                localField: '_id',
                foreignField: 'userId',
                as: 'restaurants',
                pipeline: [
                    {
                        $lookup: {
                            from: 'restaurants',
                            localField: 'restaurantId',
                            foreignField: '_id',
                            as: 'restaurantDetails',
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                        active: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $match: { 'restaurantDetails.active': true },
                    },
                    {
                        $addFields: {
                            restaurantDetails: { $arrayElemAt: ['$restaurantDetails', 0] },
                        },
                    },
                ],
            },
        });

        const users = await this._usersRepository.aggregate(pipeline, {
            readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
            comment: 'adminSearchUsersWithoutText',
        });

        return {
            data: users.map((user) => this._toDto(user)),
            total,
        };
    }

    private _toDto(user: any): AdminSearchUsersUserDto {
        return {
            _id: user._id.toString(),
            name: user.name,
            lastname: user.lastname,
            email: user.email,
            role: user.role,
            verified: user.verified,
            caslRole: user.caslRole,
            defaultLanguage: user.defaultLanguage,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            phone: user.phone,
            organizations:
                user.organizations?.map((org: any) => ({
                    _id: org._id.toString(),
                    name: org.name,
                    limit: org.limit,
                    verifiedEmailsForCampaigns: org.verifiedEmailsForCampaigns,
                })) || [],
            restaurants:
                user.restaurants?.map((restaurant: any) => ({
                    _id: restaurant._id.toString(),
                    userId: restaurant.userId.toString(),
                    caslRole: restaurant.caslRole,
                    restaurantId: restaurant.restaurantId.toString(),
                    restaurantDetails: {
                        _id: restaurant.restaurantDetails._id.toString(),
                        name: restaurant.restaurantDetails.name,
                        active: restaurant.restaurantDetails.active,
                    },
                })) || [],
        };
    }
}
