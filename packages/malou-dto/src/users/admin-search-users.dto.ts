import { z } from 'zod';

import { ApplicationLanguage, CaslRole, Role, UserCaslRole } from '@malou-io/package-utils';

export const adminSearchUsersQueryValidator = z.object({
    text: z.string().optional(),
    limit: z.string().transform(Number).optional(),
    offset: z.string().transform(Number).optional(),
});

export type AdminSearchUsersQueryDto = z.infer<typeof adminSearchUsersQueryValidator>;

export interface AdminSearchUsersUserDto {
    _id: string;
    name?: string;
    lastname?: string;
    caslRole: UserCaslRole;
    email: string;
    role: Role;
    verified: boolean;
    phone?: {
        prefix: number;
        digits: number;
    };
    defaultLanguage?: ApplicationLanguage;
    createdAt: Date;
    updatedAt: Date;
    organizations: AdminSearchUsersOrganizationDto[];
    restaurants: AdminSearchUsersRestaurantDto[];
}

export interface AdminSearchUsersOrganizationDto {
    _id: string;
    name: string;
    limit: number;
    verifiedEmailsForCampaigns: string[];
}

export interface AdminSearchUsersRestaurantDto {
    _id: string;
    userId: string;
    restaurantId: string;
    caslRole: CaslRole;
    restaurantDetails: {
        _id: string;
        name: string;
        active: boolean;
    };
}

export interface AdminSearchUsersResponseDto {
    data: AdminSearchUsersUserDto[];
    total: number;
}
